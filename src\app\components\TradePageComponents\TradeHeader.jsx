import React from 'react';
import CountDownTimer from '../CountDownTimer/page';
import styles from '../../pages/trade/search_results.module.css';

const TradeHeader = ({ 
  tradeData, 
  orderNumber, 
  timeLimit, 
  passedType 
}) => {
  return (
    <div className={styles.topBoxWrapper}>
      <div className={styles.topLeftBox}>
        <div className={styles.headerBtn}>
          <span className={styles.HeaderBuyBtn}>
            {tradeData?.flag === "user" ? "BUY" : "SELL"}
          </span>{" "}
          {tradeData?.currency_to}{" "}
          {tradeData?.flag === "user" ? "with" : "for"}{" "}
          {tradeData?.currency_from}{" "}
          {tradeData?.flag === "user" ? "from" : "to"}{" "}
          {tradeData?.flag === "user"
            ? tradeData?.peer_details?.username
            : tradeData?.user_details?.username}
        </div>
        <div className={styles.tradeStatusContainer}>
          <div className={styles.timeSubHeader}>
            <span className={styles.statusLabel}>Trade Status:</span>
            <span className={styles.statusValue}>
              {timeLimit?.status || "active"}
            </span>
            <CountDownTimer
              duration={timeLimit?.left_time_in_milliseconds}
              tradeStatus={timeLimit?.status}
              className={styles.countDownTimerInline}
            />
          </div>
        </div>
      </div>

      <div className={styles.topMiddleBox}>
        <div className={styles.rightheaderinfo}>
          <div className={styles.rinfo}>
            Order number - {orderNumber}
          </div>
          <div className={styles.rinfo}>
            Time created -{" "}
            {timeLimit?.created_date
              ? new Date(timeLimit.created_date).toLocaleString()
              : "N/A"}
          </div>
          <div className={styles.rinfo}>
            Time limit - {tradeData?.listing_data?.time_limit} minutes
          </div>
          {timeLimit?.expiry_date && (
            <div className={styles.rinfo}>
              Expires at -{" "}
              {new Date(timeLimit.expiry_date).toLocaleString()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TradeHeader;
