import React from 'react';
import styles from '../../pages/trade/search_results.module.css';

const TradeInstructions = ({ 
  activeStep, 
  tradeData, 
  timeLimit, 
  showReviewModal 
}) => {
  const renderInstructionContent = () => {
    if (timeLimit?.status === "completed" || timeLimit?.status === "expired") {
      return null;
    }

    switch (activeStep) {
      case 1:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Click on the Pay to peer Button to confirm payment to peer
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Please wait for the user to send payment to peer
              </div>
            )}
          </span>
        );
      
      case 2:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Wait for the Peer to mark the payment as Received
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Click on the Payment Received button to confirm payment received from the user
              </div>
            )}
          </span>
        );
      
      case 3:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Wait for the Peer to Send the payment to you
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Click on the Paid to User button to after sending the payment to the sender
              </div>
            )}
          </span>
        );
      
      case 4:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Click on the Payment received button to confirm payment received from the peer
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Wait for the user to confirm payment received from you
              </div>
            )}
          </span>
        );
      
      case 5:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Trade Completed
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Trade Completed
              </div>
            )}
          </span>
        );
      
      case 6:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Click on the Payment received button to confirm payment received from the peer
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Wait for the user to confirm payment received from you
              </div>
            )}
          </span>
        );
      
      case 7:
        return (
          <span>
            {tradeData?.flag === "user" ? (
              <div className={styles.dialogueSteps}>
                Click on the Trade Completed button to mark the trade as completed
              </div>
            ) : (
              <div className={styles.dialogueSteps}>
                Click on the Trade Completed button to mark the trade as completed
              </div>
            )}
          </span>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={styles.nextStepArea}>
      {renderInstructionContent()}
    </div>
  );
};

export default TradeInstructions;
