import React from 'react';
import ReportTrade from '../ReportTrade/page';
import styles from '../../pages/trade/search_results.module.css';

const TradeActions = ({ 
  tradeData,
  timeLimit,
  showReviewModal,
  btnNameUser,
  btnNamePeer,
  sendPaymentFromSenderToPeer,
  sendPaymentFromPeerToSender,
  modalIsOpen,
  openModal,
  closeModal,
  orderNumber
}) => {
  const isTradeCompleted = timeLimit?.status === "completed" || timeLimit?.status === "expired";
  
  const getButtonOpacity = () => isTradeCompleted ? 0.5 : 1;
  const getButtonCursor = () => isTradeCompleted ? "not-allowed" : "pointer";
  
  const handleUserAction = () => {
    if (!isTradeCompleted) {
      sendPaymentFromSenderToPeer();
    }
  };
  
  const handlePeerAction = () => {
    if (!isTradeCompleted) {
      sendPaymentFromPeerToSender();
    }
  };

  const getButtonText = () => {
    if (tradeData?.flag !== "user") {
      return timeLimit?.status === "completed" ? "Trade Completed" : btnNamePeer;
    } else {
      return timeLimit?.status === "completed" || showReviewModal ? "Trade Completed" : btnNameUser;
    }
  };

  const getButtonClass = () => {
    return `${styles.notifySellerBtn} ${isTradeCompleted ? styles.disabledBtn : ""}`;
  };

  return (
    <div className={styles.activityBtnArea}>
      <div className={styles.leftBtns}>
        <div
          className={getButtonClass()}
          onClick={tradeData?.flag !== "user" ? handlePeerAction : handleUserAction}
          style={{
            opacity: getButtonOpacity(),
            cursor: getButtonCursor(),
          }}
        >
          {getButtonText()}
        </div>
      </div>

      <div className={styles.reportBtn} onClick={openModal}>
        Report
      </div>
      
      <ReportTrade
        title="Report Trade"
        orderNumber={orderNumber}
        isOpen={modalIsOpen}
        onClose={closeModal}
      />
    </div>
  );
};

export default TradeActions;
